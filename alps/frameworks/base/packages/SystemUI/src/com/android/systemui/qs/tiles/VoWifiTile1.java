package com.android.systemui.qs.tiles;

import static com.android.systemui.qs.QSHost.isOrangeShowTwoVoWifiTile;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.service.quicksettings.Tile;
import android.telephony.CarrierConfigManager;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.widget.Switch;

import com.android.ims.ImsManager;
import com.android.internal.logging.MetricsLogger;
import com.android.systemui.animation.Expandable;
import com.android.systemui.dagger.qualifiers.Background;
import com.android.systemui.dagger.qualifiers.Main;
import com.android.systemui.plugins.ActivityStarter;
import com.android.systemui.plugins.FalsingManager;
import com.android.systemui.plugins.qs.QSTile;
import com.android.systemui.plugins.statusbar.StatusBarStateController;
import com.android.systemui.qs.QSHost;
import com.android.systemui.qs.QsEventLogger;
import com.android.systemui.qs.logging.QSLogger;
import com.android.systemui.qs.tileimpl.QSTileImpl;
import com.android.systemui.res.R;

import javax.inject.Inject;

//VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 begin
import android.ecid.EcidConfigManager;
import android.os.PersistableBundle;
//VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 end


/** Quick settings tile: VoWifi1 by yangyang **/
public class VoWifiTile1 extends QSTileImpl<QSTile.BooleanState> {

    public static final String TILE_SPEC = "vowifi1";
    private final Icon mIcon = ResourceIcon.get(R.drawable.ic_qs_vowifi_on);
    private static final String TAG = "VoWifi1";
    private boolean isVoWifi;
    private ImsManager mImsManager;
    private int mDefaultDataSubId = 0;
    private int mPhoneId = 0;

    private boolean showVoWifiTile;


    @Inject
    @TargetApi(Build.VERSION_CODES.N)
    protected VoWifiTile1(QSHost host,
                          QsEventLogger uiEventLogger,
                          @Background Looper backgroundLooper,
                          @Main Handler mainHandler,
                          FalsingManager falsingManager,
                          MetricsLogger metricsLogger,
                          StatusBarStateController statusBarStateController,
                          ActivityStarter activityStarter,
                          QSLogger qsLogger) {
        super(host, uiEventLogger, backgroundLooper, mainHandler, falsingManager, metricsLogger, statusBarStateController, activityStarter, qsLogger);
        mDefaultDataSubId = SubscriptionManager.getDefaultDataSubscriptionId();
        mPhoneId = SubscriptionManager.getPhoneId(mDefaultDataSubId);
        mImsManager = ImsManager.getInstance(mContext, mPhoneId);
        mDefaultDataSubId = SubscriptionManager.getDefaultDataSubscriptionId();
    }

    @Override
    public BooleanState newTileState() {
        return new BooleanState();
    }

    @Override
    protected void handleDestroy() {
        super.handleDestroy();
    }

    @Override
    protected void handleClick(Expandable expandable) {
        if (getState().state == Tile.STATE_UNAVAILABLE) {
            return;
        }
        isVoWifi = !mState.value;
        Log.d(TAG, "handleClick isVoWifi = " + isVoWifi);
        mImsManager.setWfcSetting(isVoWifi);
        refreshState(isVoWifi);
    }

    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    @Override
    protected void handleUpdateState(BooleanState state, Object arg) {
        mDefaultDataSubId = SubscriptionManager.getDefaultDataSubscriptionId();
        if (isOrangeShowTwoVoWifiTile(mContext)){
            mDefaultDataSubId = SubscriptionManager.getSubscriptionId(1);
        }else {
            mHost.removeTile(getTileSpec());
        }
        mPhoneId = SubscriptionManager.getPhoneId(mDefaultDataSubId);
        //VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 begin
        mImsManager = ImsManager.getInstance(mContext, mPhoneId);
        EcidConfigManager ecidConfigManager = EcidConfigManager.getInstance(mContext);
        PersistableBundle persistableBundle = ecidConfigManager.getEcidConfig();
        if (persistableBundle != null && persistableBundle.containsKey(EcidConfigManager.KEY_ECID_SHOW_QS_VOWIFI_TILE_CONFIG)
            && persistableBundle.getBoolean(EcidConfigManager.KEY_ECID_SHOW_QS_VOWIFI_TILE_CONFIG)) {
            showVoWifiTile = persistableBundle.getBoolean(EcidConfigManager.KEY_ECID_SHOW_QS_VOWIFI_TILE_CONFIG);
        }else{
        showVoWifiTile = isCarrierConfigManagerKeyEnabled(
                CarrierConfigManager.KEY_SHOW_QS_VOWIFI_TILE_CONFIG, mDefaultDataSubId, false);
        }
        //VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 end
        if (!showVoWifiTile){
            mHost.removeTile(getTileSpec());
            Log.d(TAG, "Tile removed. VoLte no longer available");
        }
        isVoWifi = mImsManager.isWfcEnabledByUser();
        //更新 state.value 状态
        if (arg instanceof Boolean) {
            boolean value = (Boolean) arg;
            Log.d(TAG, "[handleUpdateState] value = " + value);
            if (value == state.value) {
                return;
            }
            state.value = value;
        } else {
            state.value = isVoWifi;
        }
        boolean available = mImsManager.isWfcEnabledByPlatform();
        state.label = mHost.getContext().getString(R.string.quick_settings_vowifi_label);
        state.icon = mIcon;
        state.contentDescription = state.label;
        state.expandedAccessibilityClassName = Switch.class.getName();
        Log.d(TAG, "[handleUpdateState] state.value = " + state.value + " available = " + available);
        //DAHLIA-2203 : modify for VoLTE and VoWiFi dropdown icons by yangyang 2024.12.26 start
        if (SubscriptionManager.isValidPhoneId(mPhoneId) && available) {
            state.state = state.value ? Tile.STATE_ACTIVE : Tile.STATE_INACTIVE;
        } else {
            state.state = Tile.STATE_UNAVAILABLE;
        }
        //DAHLIA-2203 : modify for VoLTE and VoWiFi dropdown icons by yangyang 2024.12.26 end
    }

    @Override
    public Intent getLongClickIntent() {
        if (mImsManager.isVolteEnabledByPlatform()) {
            Intent mVoLTEHandleIntent = new Intent("android.settings.WIFI_CALLING_SETTINGS");
            mVoLTEHandleIntent.putExtra(Settings.EXTRA_SUB_ID, SubscriptionManager.getDefaultDataSubscriptionId());
            return mVoLTEHandleIntent;
        } else {
            return new Intent();

        }
    }

    @Override
    public CharSequence getTileLabel() {
        return mContext.getString(R.string.quick_settings_vowifi_label);
    }
}
