/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.qs.tiles.impl.battery.domain.model

/** BatterySaver mode tile model. */
sealed interface BatterySaverTileModel {

    val isPluggedIn: Boolean
    val isPowerSaving: Boolean

    /** For when the device does not support extreme battery saver mode. */
    data class Standard(
        override val isPluggedIn: Boolean,
        override val isPowerSaving: Boolean,
    ) : BatterySaverTileModel

    /**
     * For when device supports extreme battery saver mode. Whether or not that mode is enabled is
     * determined through [isExtremeSaving].
     */
    data class Extreme(
        override val isPluggedIn: <PERSON>olean,
        override val isPowerSaving: <PERSON><PERSON><PERSON>,
        val isExtremeSaving: <PERSON>olean,
    ) : BatterySaverTileModel
}
