package com.android.systemui.qs.tiles;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.service.quicksettings.Tile;
//DAHLIA-302: modify for  VoLTE and VoWiFi dropdown icons by yangyang 2024.11.23 start
import android.telephony.CarrierConfigManager;
//DAHLIA-302: modify for  VoLTE and VoWiFi dropdown icons by yangyang 2024.11.23 end
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.util.Log;
import com.android.systemui.res.R;

import com.android.ims.ImsManager;
import com.android.internal.logging.MetricsLogger;
import com.android.systemui.animation.Expandable;
import com.android.systemui.dagger.qualifiers.Background;
import com.android.systemui.dagger.qualifiers.Main;
import com.android.systemui.plugins.ActivityStarter;
import com.android.systemui.plugins.FalsingManager;
import com.android.systemui.plugins.qs.QSTile;
import com.android.systemui.plugins.statusbar.StatusBarStateController;
import com.android.systemui.qs.QSHost;
import com.android.systemui.qs.QsEventLogger;
import com.android.systemui.qs.logging.QSLogger;
import com.android.systemui.qs.tileimpl.QSTileImpl;
import android.widget.Switch;
import javax.inject.Inject;
//VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 begin
import android.ecid.EcidConfigManager;
import android.os.PersistableBundle;
//VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 end


/** Quick settings tile: VoLte by yangyang **/
/* DAHLIA-256: modify for No shotcut Icon VoLTE on display when slide down by yangyang 2024.11.20 */
public class VoLteTile extends QSTileImpl<QSTile.BooleanState> {

    public static final String TILE_SPEC = "volte";
    private final Icon mIcon = ResourceIcon.get(R.drawable.ic_qs_volte);
    private static final String TAG = "voLte";
    private boolean isVoLte;
    private ImsManager mImsManager;
    private int mDefaultDataSubId = 0;
    private int mPhoneId = 0;

    //DAHLIA-302: modify for  VoLTE and VoWiFi dropdown icons by yangyang 2024.11.23 start
    private boolean showVoLteTile;
    //DAHLIA-302: modify for  VoLTE and VoWiFi dropdown icons by yangyang 2024.11.23 end

    @Inject
    @TargetApi(Build.VERSION_CODES.N)
    protected VoLteTile(QSHost host,
                        QsEventLogger uiEventLogger,
                        @Background Looper backgroundLooper,
                        @Main Handler mainHandler,
                        FalsingManager falsingManager,
                        MetricsLogger metricsLogger,
                        StatusBarStateController statusBarStateController,
                        ActivityStarter activityStarter,
                        QSLogger qsLogger) {
        super(host, uiEventLogger, backgroundLooper, mainHandler, falsingManager, metricsLogger, statusBarStateController, activityStarter, qsLogger);
        mDefaultDataSubId = SubscriptionManager.getDefaultDataSubscriptionId();
        mPhoneId = SubscriptionManager.getPhoneId(mDefaultDataSubId);
        mImsManager = ImsManager.getInstance(mContext, mPhoneId);
    }

    @Override
    public BooleanState newTileState() {
        return new BooleanState();
    }

    @Override
    protected void handleDestroy() {
        super.handleDestroy();
    }

    @Override
    protected void handleClick(Expandable expandable) {
        if (getState().state == Tile.STATE_UNAVAILABLE) {
            return;
        }
        isVoLte = !mState.value;
        Log.d(TAG, "handleClick isVoLte = " + isVoLte);
        mImsManager.setEnhanced4gLteModeSetting(isVoLte);
        refreshState(isVoLte);
    }

    @Override
    protected void handleUpdateState(BooleanState state, Object arg) {
        //DAHLIA-302: modify for  VoLTE and VoWiFi dropdown icons by yangyang 2024.11.23 start
        mDefaultDataSubId = SubscriptionManager.getDefaultDataSubscriptionId();
        //VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 begin
        EcidConfigManager ecidConfigManager = EcidConfigManager.getInstance(mContext);
        PersistableBundle persistableBundle = ecidConfigManager.getEcidConfig();
        if (persistableBundle != null && persistableBundle.containsKey(EcidConfigManager.KEY_ECID_SHOW_QS_VOLTE_TILE_CONFIG)
            && persistableBundle.getBoolean(EcidConfigManager.KEY_ECID_SHOW_QS_VOLTE_TILE_CONFIG)) {
            showVoLteTile = persistableBundle.getBoolean(EcidConfigManager.KEY_ECID_SHOW_QS_VOLTE_TILE_CONFIG);
        }else{
            showVoLteTile = isCarrierConfigManagerKeyEnabled(
                CarrierConfigManager.KEY_SHOW_QS_VOLTE_TILE_CONFIG, mDefaultDataSubId, false);
        }
        //VANILLA-424 xuxiaomeng modify for [AVAR]WIFI Calling meun in drop down menu 2025.3.4 end
        if (!showVoLteTile){
            mHost.removeTile(getTileSpec());
            Log.d(TAG, "Tile removed. VoLte no longer available");
        }
        //DAHLIA-302: modify for  VoLTE and VoWiFi dropdown icons by yangyang 2024.11.23 end
        mPhoneId = SubscriptionManager.getPhoneId(mDefaultDataSubId);
        mImsManager = ImsManager.getInstance(mContext, mPhoneId);
        isVoLte = mImsManager.isEnhanced4gLteModeSettingEnabledByUser();
        //更新 state.value 状态
        if (arg instanceof Boolean) {
            boolean value = (Boolean) arg;
            Log.d(TAG, "[handleUpdateState] value = " + value);
            if (value == state.value) {
                return;
            }
            state.value = value;
        } else {
            state.value = isVoLte;
        }
        boolean available = mImsManager.isVolteEnabledByPlatform();
        state.label = mHost.getContext().getString(R.string.qs_volte_call_title);
        state.icon = mIcon;
        state.contentDescription = state.label;
        state.expandedAccessibilityClassName = Switch.class.getName();
        Log.d(TAG, "[handleUpdateState] state.value = " + state.value + " available = " + available);
        //DAHLIA-2203 : modify for VoLTE and VoWiFi dropdown icons by yangyang 2024.12.26 start
        if (SubscriptionManager.isValidPhoneId(mPhoneId) && available) {
            state.state = state.value ? Tile.STATE_ACTIVE : Tile.STATE_INACTIVE;
        } else {
            state.state = Tile.STATE_UNAVAILABLE;
        }
        //DAHLIA-2203 : modify for VoLTE and VoWiFi dropdown icons by yangyang 2024.12.26 end
    }

    @Override
    public Intent getLongClickIntent() {
        if (mImsManager.isVolteEnabledByPlatform()) {
            Intent mVoLTEHandleIntent = new Intent("android.settings.NETWORK_OPERATOR_SETTINGS");
            mVoLTEHandleIntent.putExtra(Settings.EXTRA_SUB_ID, SubscriptionManager.getDefaultDataSubscriptionId());
            return mVoLTEHandleIntent;
        } else {
            return new Intent();

        }
    }

    @Override
    public CharSequence getTileLabel() {
        return mContext.getString(R.string.qs_volte_call_title);
    }
}
