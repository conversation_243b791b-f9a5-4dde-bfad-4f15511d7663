/*
 * Copyright (C) 2014 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License
 */

package com.android.systemui.qs.tiles;

import android.app.ActivityManager;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.service.quicksettings.Tile;
import android.widget.Switch;

import androidx.annotation.Nullable;

import com.android.internal.logging.MetricsLogger;
import com.android.internal.logging.nano.MetricsProto.MetricsEvent;
import com.android.systemui.animation.Expandable;
import com.android.systemui.dagger.qualifiers.Background;
import com.android.systemui.dagger.qualifiers.Main;
import com.android.systemui.plugins.ActivityStarter;
import com.android.systemui.plugins.FalsingManager;
import com.android.systemui.plugins.qs.QSTile.BooleanState;
import com.android.systemui.plugins.statusbar.StatusBarStateController;
import com.android.systemui.qs.QSHost;
import com.android.systemui.qs.QsEventLogger;
import com.android.systemui.qs.logging.QSLogger;
import com.android.systemui.qs.tileimpl.QSTileImpl;
import com.android.systemui.res.R;
import com.android.systemui.statusbar.policy.FlashlightController;
import com.unisoc.systemui.statusbar.policy.UniFlashlightController;

import javax.inject.Inject;
//redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian start
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
//redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian end
// DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 start
import android.util.Log;
import javax.inject.Inject;
import android.app.NotificationManager;
import android.app.NotificationChannel;
import androidx.core.app.NotificationCompat;
import java.util.concurrent.TimeUnit;
import android.os.Build;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.CountDownTimer;
import com.android.systemui.statusbar.policy.AttentionDialog;
// DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 end
/**
 * Quick settings tile: Control flashlight
 **/
public class FlashlightTile extends QSTileImpl<BooleanState> implements
        FlashlightController.FlashlightListener {

    public static final String TILE_SPEC = "flashlight";
    private final FlashlightController mFlashlightController;
    // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 start
    // Record the time stamp when the flashlight starts to turn on (milliseconds)
    private long mFlashlightStartTime;
    // Maximum allowed open time (15 minutes to milliseconds)
    private static final long MAX_FLASHLIGHT_DURATION = TimeUnit.MINUTES.toMillis(15);
    private Handler mHandler;
    private NotificationManager mNotificationManager; // It is used to manage operations such as sending notifications
    private static final long CHECK_INTERVAL = TimeUnit.MINUTES.toMillis(1); // Set the periodic check interval to 1 minute
    private Runnable mDurationCheckRunnable = new Runnable() {
        @Override
        public void run() {
            checkFlashlightDuration();
            //mHandler.postDelayed(this, CHECK_INTERVAL);//DAHLIA-554 systemui OOM remove the postDelayed zjz 20250116
        }
    };
    // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 end
    @Inject
    public FlashlightTile(
            QSHost host,
            QsEventLogger uiEventLogger,
            @Background Looper backgroundLooper,
            @Main Handler mainHandler,
            FalsingManager falsingManager,
            MetricsLogger metricsLogger,
            StatusBarStateController statusBarStateController,
            ActivityStarter activityStarter,
            QSLogger qsLogger,
            FlashlightController flashlightController
    ) {
        super(host, uiEventLogger, backgroundLooper, mainHandler, falsingManager, metricsLogger,
                statusBarStateController, activityStarter, qsLogger);
        mFlashlightController = flashlightController;
        mFlashlightController.observe(getLifecycle(), this);
        // AR.695.001753.014831.042977(1/2) BUG 2587146 - temperature control falshlight.
        UniFlashlightController.getInstance().init(flashlightController);
        //redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian start
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_SHUTDOWN);
        mContext.registerReceiver(mBroadcastReceiver, intentFilter);
        //redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian end
        // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 start
        mHandler = mainHandler;
        // Gets the NotificationManager object for subsequent notification
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            mNotificationManager = mContext.getSystemService(NotificationManager.class);
            Log.d(TAG, "Got NotificationManager instance."); // Print information about the notification management object
            //Create notification channels (only once on Android 8.0 and above)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel channel = new NotificationChannel("flashlight_channel",
                        "Flashlight Notification",
                        NotificationManager.IMPORTANCE_DEFAULT);
                mNotificationManager.createNotificationChannel(channel);
                Log.d(TAG, "Created notification channel: flashlight_channel."); // Prints the creation of notification channels
            }
        }
        // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 end
    }

    @Override
    protected void handleDestroy() {
        super.handleDestroy();
        //redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian start
        try {
            mContext.unregisterReceiver(mBroadcastReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian end
    }

    @Override
    public BooleanState newTileState() {
        BooleanState state = new BooleanState();
        state.handlesLongClick = false;
        return state;
    }

    @Override
    protected void handleUserSwitch(int newUserId) {
    }

    @Override
    public Intent getLongClickIntent() {
        return new Intent(MediaStore.INTENT_ACTION_STILL_IMAGE_CAMERA);
    }

    @Override
    public boolean isAvailable() {
        return mFlashlightController.hasFlashlight();
    }

    @Override
    protected void handleClick(@Nullable Expandable expandable) {
        if (ActivityManager.isUserAMonkey()) {
            return;
        }
        boolean newState = !mState.value;
        refreshState(newState);
        mFlashlightController.setFlashlight(newState);
        // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 start
        if (newState) {
            mFlashlightStartTime = System.currentTimeMillis();
            startDurationCheck();
        }
        // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 end
    }

    @Override
    public CharSequence getTileLabel() {
        return mContext.getString(R.string.quick_settings_flashlight_label);
    }

    @Override
    protected void handleLongClick(@Nullable Expandable expandable) {
        handleClick(expandable);
    }

    @Override
    protected void handleUpdateState(BooleanState state, Object arg) {
        state.label = mHost.getContext().getString(R.string.quick_settings_flashlight_label);
        state.secondaryLabel = "";
        state.stateDescription = "";
        if (!mFlashlightController.isAvailable()) {
            state.secondaryLabel = mContext.getString(
                    R.string.quick_settings_flashlight_camera_in_use);
            state.stateDescription = state.secondaryLabel;
            state.state = Tile.STATE_UNAVAILABLE;
            state.icon = ResourceIcon.get(R.drawable.qs_flashlight_icon_off);
            return;
        }
        // AR.695.001753.014831.042977(2/2)
        if (UniFlashlightController.getInstance().isThermal()) {
            state.secondaryLabel = UniFlashlightController
                    .getInstance().getSecondaryLabel(mContext);
            state.stateDescription = state.secondaryLabel;
            state.state = Tile.STATE_UNAVAILABLE;
            state.icon = ResourceIcon.get(R.drawable.qs_flashlight_icon_off);
            return;
        }
        if (arg instanceof Boolean) {
            boolean value = (Boolean) arg;
            if (value == state.value) {
                return;
            }
            state.value = value;
        } else {
            state.value = mFlashlightController.isEnabled();
        }
        state.contentDescription = mContext.getString(R.string.quick_settings_flashlight_label);
        state.expandedAccessibilityClassName = Switch.class.getName();
        state.state = state.value ? Tile.STATE_ACTIVE : Tile.STATE_INACTIVE;
        state.icon = ResourceIcon.get(state.value
                ? R.drawable.qs_flashlight_icon_on : R.drawable.qs_flashlight_icon_off);
    }
    // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 start
    private void startDurationCheck() {
        mHandler.postDelayed(mDurationCheckRunnable, CHECK_INTERVAL);
    }
    private void checkFlashlightDuration() {
        if (mFlashlightController.isEnabled()) {
            long currentTime = System.currentTimeMillis();
            long elapsedTime = currentTime - mFlashlightStartTime;
            Log.d(TAG, "Checking flashlight duration, elapsed time: " + elapsedTime + " ms.");
            if (elapsedTime >= MAX_FLASHLIGHT_DURATION) {
                Log.d(TAG, "Flashlight has been on for 15 minutes, sending notification and turning it off.");
                showDurationDialog();
                stopDurationCheck();
            } else {
                mHandler.postDelayed(mDurationCheckRunnable, CHECK_INTERVAL);
            }
        }
    }
    private void showDurationDialog() {
        Context context = mHost.getContext();
        if (context == null) {
            Log.e(TAG, "Context is null, cannot create AttentionDialog.");
            return;
        }
        AttentionDialog dialog = new AttentionDialog(context, new AttentionDialog.OnDialogButtonClickListener() {
            @Override
            public void onContinueClick() {
                // Process Continue button click
                // The user chooses to continue to use, do not do any operation, the flashlight remains on
                mFlashlightStartTime = System.currentTimeMillis();
                startDurationCheck();
            }
            @Override
            public void onTurnOffClick() {
                // Handle close button click
                // The countdown is over, and if the dialog is still displayed, turn off the flashlight and close the dialog
                sendFlashlightDurationNotification();
                mFlashlightController.setFlashlight(false);
                stopDurationCheck();
            }
        });
        dialog.show();
    }
    private void sendFlashlightDurationNotification() {
        Context context = mHost.getContext();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, "flashlight_channel")
                   .setSmallIcon(android.R.drawable.stat_sys_warning)
                   .setContentTitle(context.getString(R.string.flashlight_duration_dialog_title))
                   .setContentText(context.getString(R.string.flashlight_duration_dialog_message))
                   .setPriority(NotificationCompat.PRIORITY_DEFAULT);
            mNotificationManager.notify(0, builder.build());
            Log.d(TAG, "Sent flashlight duration notification."); 
        }
    }
    private void stopDurationCheck() {
        mHandler.removeCallbacks(mDurationCheckRunnable);
    }
    // DAHLIA-554: Add Flashlight timeout by linchengxian 2024.12.5 end

    @Override
    public int getMetricsCategory() {
        return MetricsEvent.QS_FLASHLIGHT;
    }

    @Override
    public void onFlashlightChanged(boolean enabled) {
        refreshState(enabled);
    }

    @Override
    public void onFlashlightError() {
        refreshState(false);
    }

    @Override
    public void onFlashlightAvailabilityChanged(boolean available) {
        refreshState();
    }
    // redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian start
    private final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        public void onReceive(Context context, Intent intent) {
            if (mState.value) {
                handleClick(null);
            }
        }
    };
    //redmine 300888 Restart the phone with the flashlight on, and the flashlight will only turn off after the screen starts to light up zhaoxian end
}
