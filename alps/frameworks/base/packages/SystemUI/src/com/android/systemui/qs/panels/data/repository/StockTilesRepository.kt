/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.qs.panels.data.repository

import android.content.res.Resources
import com.android.systemui.dagger.SysUISingleton
import com.android.systemui.dagger.qualifiers.Main
import com.android.systemui.qs.pipeline.shared.TileSpec
import com.android.systemui.res.R
import javax.inject.Inject

@SysUISingleton
class StockTilesRepository
@Inject
constructor(
    @Main private val resources: Resources,
) {
    /**
     * List of stock platform tiles. All of the specs will be of type [TileSpec.PlatformTileSpec].
     */
    val stockTiles =
        resources
            .getString(R.string.quick_settings_tiles_stock)
            .split(",")
            .map(TileSpec::create)
            .filterNot { it is TileSpec.Invalid }
}
