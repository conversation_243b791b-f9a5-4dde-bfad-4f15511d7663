/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package com.android.systemui.qs;

import "frameworks/base/packages/SystemUI/src/com/android/systemui/util/proto/component_name.proto";

option java_multiple_files = true;

message QsTileState {
  oneof identifier {
    string spec = 1;
    com.android.systemui.util.ComponentNameProto component_name = 2;
  }

  enum State {
    UNAVAILABLE = 0;
    INACTIVE = 1;
    ACTIVE = 2;
  }

  State state = 3;
  oneof optional_boolean_state {
    bool boolean_state = 4;
  }
  oneof optional_label {
    string label = 5;
  }
  oneof optional_secondary_label {
    string secondary_label = 6;
  }
}
