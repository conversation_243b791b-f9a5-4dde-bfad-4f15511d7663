/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.qs.tiles.base.interactor

import androidx.annotation.WorkerThread
import com.android.systemui.qs.tiles.viewmodel.QSTileConfig
import com.android.systemui.qs.tiles.viewmodel.QSTileState

interface QSTileDataToStateMapper<DATA_TYPE> {

    /**
     * Maps [DATA_TYPE] to the [QSTileState] that is then displayed by the View layer. It's called
     * on a background thread, so it's safe to perform long running operations there.
     */
    @WorkerThread fun map(config: QSTileConfig, data: DATA_TYPE): QSTileState
}
